<?php

namespace App\Imports;

use App\Events\InventoryAdjusted;
use App\Models\Collection as ProductCollection;
use App\Models\PackingGroup;
use App\Models\Price;
use App\Models\Product;
use App\Models\Protocol;
use App\Models\Tag;
use App\Models\User;
use App\Models\Vendor;
use App\Support\Enums\ProductType;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class ProductImport implements ToCollection, WithValidation, WithHeadingRow
{
    use Importable;

    protected array $packingGroups;

    public function __construct(
        public string $importType,
        public ?User $user = null
    ) {
        $this->packingGroups = PackingGroup::pluck('title', 'id')
            ->map(fn($group) => str($group)->lower()->trim()->value())
            ->toArray();
    }

    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if ($row->filter()->isEmpty()) {
                continue;
            }

            match ($this->importType) {
                'update_by_id' => $this->updateProductId($row),
                'update_by_sku' => $this->updateProductSku($row),
                default => $this->createProduct($row),
            };
        }

        return true;
    }


    private function updateProductId(Collection $row): Product
    {
        $product = Product::where('id', $row['id'])->first();

        if (isset($row['tier_quantity'])) {
            $this->updatePriceTiers($product, $row);
        }

        $attributes = $this->mapUpdateRowDataToAttributes($row, $product);

        $attributes['non_attributes']['original_inventory'] = $product->inventory;

        $product->update(Arr::except($attributes, ['id', 'non_attributes']));

        $this->resolveCollectionAndTag($attributes, $product);

        $this->adjustInventory($attributes, $product);

        return $product;
    }

    private function mapUpdateRowDataToAttributes(Collection $row, Product $product): array
    {
        return [
            'title' => $row['title'] ?? $product->title,
            'type_id' => $this->resolveTypeId($row['type'] ?? null) ?? $product->type_id,
            'summary' => $row['summary'] ?? $product->summary,
            'description' => $row['description'] ?? $product->description,
            'unit_description' => $row['unit_description'] ?? $product->unit_description,
            'ingredients' => $row['ingredients'] ?? $product->ingredients,
            'sku' => $row['sku'] ?? $product->sku,
            'weight' => $row['weight_lb'] ?? $product->weight,
            'item_cost' => $row['item_cost'] ?? money($product->item_cost),
            'inventory' => $row['on_site_inventory_actual'] ?? $product->inventory,
            'other_inventory' => $row['off_site_inventory'] ?? $product->other_inventory,
            'oos_threshold_inventory' => $row['subscription_reserve'] ?? $product->oos_threshold_inventory,
            'stock_out_inventory' => $row['reorder_threshold'] ?? $product->stock_out_inventory,
            'barcode' => $row['barcode'] ?? $product->barcode,
            'page_title' => $row['meta_title'] ?? $product->page_title,
            'seo_description' => $row['meta_description'] ?? $product->seo_description,
            'taxable' => $this->resolveTaxStatus($row['tax_status'] ?? null) ?? $product->taxable,
            'visible' => $this->resolveVisibilityStatus($row['visibility_status'] ?? null) ?? $product->visible,
            'unit_of_issue' => $this->resolveUnitOfIssue($row['pricing_unit'] ?? null) ?? $product->unit_of_issue,
            'custom_sort' => $row['storage_location'] ?? $product->custom_sort,
            'accounting_class' => $row['accounting_class_id'] ?? $product->accounting_class,
            'inventory_type' => $this->resolveInventoryTypeId($row['packing_group'] ?? null) ?? $product->packingGroup->id,
            'vendor_id' => $this->resolveVendorId($row['vendor'] ?? null) ?? $product->vendor_id,
            'non_attributes' => [
                'collection' => $row['collection'] ?? null,
                'protocol' => $row['protocol'] ?? null,
                'tag' => $row['tag'] ?? null,
                'on_site_inventory_to_add' => $row['on_site_inventory_to_add'] ?? 0,
                'on_site_inventory_to_remove' => $row['on_site_inventory_to_remove'] ?? 0,
            ]
        ];
    }

    private function resolveTypeId(?string $type): ?int
    {
        if (is_null($type)) {
            return null;
        }

        return Arr::get(ProductType::options(), Str::upper($type));
    }

    /**
     * @param $tax_status
     */
    private function resolveTaxStatus(?string $tax_status): ?bool
    {
        if (is_null($tax_status)) {
            return null;
        }

        return in_array(Str::lower($tax_status), ['on', 'true', 'yes']);
    }

    private function resolveVisibilityStatus(?string $visibility_status): ?bool
    {
        if (is_null($visibility_status)) {
            return null;
        }

        return in_array(Str::lower($visibility_status), ['visible', 'on', 'true', 'yes']);
    }

    private function resolveUnitOfIssue(?string $pricing_unit): ?string
    {
        if (is_null($pricing_unit)) {
            return null;
        }

        return Arr::get(['weight' => 'weight', 'package' => 'package'], Str::lower($pricing_unit));
    }

    private function resolveInventoryTypeId(?string $packing_group): ?int
    {
        if (is_null($packing_group)) {
            return null;
        }

        return array_search(
            Str::lower($packing_group),
            $this->packingGroups
        );
    }


    private function resolveVendorId(?string $vendor): ?int
    {
        if (is_null($vendor)) {
            return null;
        }

        $vendor = Vendor::firstOrCreate(['title' => $vendor], ['cover_photo' => '','description' => '']);

        return $vendor->id;
    }

    private function resolveCollectionAndTag(array $attributes, Product $product): void
    {
        if ($collectionAttributes = Arr::get($attributes, 'non_attributes.collection')) {
            collect(explode(',', $collectionAttributes))->each(function ($collectionAttribute) use ($product) {
                $collection = ProductCollection::firstOrCreate(['title' => trim($collectionAttribute)], [
                    'description' => '',
                    'icon_photo' => '',
                    'cover_photo' => '',
                ]);

                $product->collections()->syncWithoutDetaching($collection);
            });
        }

        if ($tagAttributes = Arr::get($attributes, 'non_attributes.tag')) {
            collect(explode(',', $tagAttributes))->each(function ($tagAttribute) use ($product) {
                $tag = Tag::firstOrCreate(['title' => trim($tagAttribute)]);

                $product->tags()->syncWithoutDetaching($tag);
            });
        }

        if ($protocolAttributes = Arr::get($attributes, 'non_attributes.protocol')) {
            collect(explode(',', $protocolAttributes))->each(function ($protocolTitle) use ($product) {
                $protocol = Protocol::firstOrCreate(['title' => trim($protocolTitle)]);

                $product->protocols()->syncWithoutDetaching($protocol);
            });
        }
    }

    public function adjustInventory(array $attributes, Product $product): void
    {
        $originalInventory = Arr::get($attributes, 'non_attributes.original_inventory');

        if (Arr::get($attributes, 'non_attributes.on_site_inventory_to_add') > 0) {
            $inventoryToAdd = (int)Arr::get($attributes, 'non_attributes.on_site_inventory_to_add');
            $product->incrementInventory($inventoryToAdd);
        }

        if (Arr::get($attributes, 'non_attributes.on_site_inventory_to_remove') > 0) {
            $inventoryToRemove = (int)Arr::get($attributes, 'non_attributes.on_site_inventory_to_remove');
            $product->decrementInventory($inventoryToRemove);
        }

        if ($product->wasChanged('inventory')) {
            event(new InventoryAdjusted(
                $product->id,
                $originalInventory,
                $product->inventory
            ));
        }
    }

    private function updateProductSku(Collection $row): Product
    {
        $product = Product::where('sku', $row['sku'])->firstOrFail();

        if (isset($row['tier_quantity'])) {
            $this->updatePriceTiers($product, $row);
        }

        $attributes = $this->mapUpdateRowDataToAttributes($row, $product);

        $attributes['non_attributes']['original_inventory'] = $product->inventory;

        $product->update(Arr::except($attributes, ['id', 'sku', 'non_attributes']));

        $this->resolveCollectionAndTag($attributes, $product);

        $this->adjustInventory($attributes, $product);

        return $product;
    }

    private function createProduct(Collection $row): Product
    {
        $existingProduct = !empty($row['sku'])
            ? Product::where('sku', $row['sku'])->first()
            : (!empty($row['title']) ? Product::where('title', $row['title'])->first() : null);

        if ($existingProduct && isset($row['tier_quantity'])) {
            $this->updatePriceTiers($existingProduct, $row);

            return $existingProduct;
        }

        $attributes = [
            'title' => $row['title'],
            'type_id' => $this->resolveTypeId($row['type'] ?? 'standard') ?? 1,
            'summary' => $row['summary'] ?? null,
            'description' => $row['description'] ?? null,
            'unit_description' => $row['unit_description'] ?? null,
            'ingredients' => $row['ingredients'] ?? null,
            'sku' => $row['sku'] ?? null,
            'weight' => $row['weight_lb'] ?? 0,
            'unit_price' => $row['tier_unit_price'],
            'sale_unit_price' => $row['tier_sale_unit_price']  ?? null,
            'item_cost' => $row['item_cost'] ?? null,
            'inventory' => $row['on_site_inventory_actual'] ?? 0,
            'other_inventory' => $row['off_site_inventory'] ?? 0,
            'oos_threshold_inventory' => $row['subscription_reserve'] ?? null,
            'stock_out_inventory' => $row['reorder_threshold'] ?? 0,
            'barcode' => $row['barcode'] ?? null,
            'page_title' => $row['meta_title'] ?? null,
            'seo_description' => $row['meta_description'] ?? null,
            'taxable' => $this->resolveTaxStatus($row['tax_status'] ?? null) ?? false,
            'visible' => $this->resolveVisibilityStatus($row['visibility_status'] ?? null) ?? false,
            'unit_of_issue' => $this->resolveUnitOfIssue($row['pricing_unit'] ?? null) ?? 'package',
            'custom_sort' => $row['storage_location'] ?? null,
            'accounting_class' => $row['accounting_class_id'] ?? null,
            'inventory_type' => $this->resolveInventoryTypeId($row['packing_group'] ?? null) ?? PackingGroup::frozen(),
            'vendor_id' => $this->resolveVendorId($row['vendor'] ?? null),
            'non_attributes' => [
                'collection' => $row['collection'] ?? null,
                'protocol' => $row['protocol'] ?? null,
                'tag' => $row['tag'] ?? null,
                'inventory_to_add' => $row['on_site_inventory_to_add'] ?? 0,
                'inventory_to_remove' => $row['on_site_inventory_to_remove'] ?? 0,
            ]
        ];

        $product = Product::create(Arr::except($attributes, ['id', 'non_attributes']));

        $this->resolveCollectionAndTag($attributes, $product);

        return $product;
    }

    public function rules(): array
    {
        return [
            'id' => ['nullable', Rule::when(fn() => $this->importType === 'update_by_id', [
                'required',
                Rule::exists('products', 'id')->withoutTrashed()])],
            'title' => ['nullable', 'string', Rule::when(fn() => $this->importType === 'create', ['required'])],
            'type' => ['nullable', Rule::when(fn() => $this->importType === 'create', [
                'required',
                Rule::in(ProductType::options()->keys()->map(fn($type) => Str::lower($type))->toArray())
            ])],
            'weight_lb' => ['nullable', 'numeric',],
            'tier_unit_price' => ['nullable', 'numeric', 'min:0', 'required_with:tier_quantity', Rule::when(fn() => $this->importType === 'create', ['required'])],
            'tier_sale_unit_price' => ['nullable', 'numeric', 'min:0', 'required_with:tier_quantity'],
            'summary' => ['nullable', 'string'],
            'description' => ['nullable', 'string'],
            'protocol' => ['nullable', 'string'],
            'collection' => ['nullable', 'string'],
            'tag' => ['nullable', 'string'],
            'unit_description' => ['nullable', 'string', 'max:255'],
            'ingredients' => ['nullable', 'string'],
            'sku' => ['nullable',
                Rule::when(fn () => $this->importType === 'create', [Rule::unique('products', 'sku')]),
                Rule::when(fn () => $this->importType === 'update_by_id', [Rule::unique('products', 'sku')]),
                Rule::when(fn () => $this->importType === 'update_by_sku', ['required', Rule::exists('products', 'sku')->withoutTrashed(),])
            ],
            'pricing_unit' => ['sometimes', 'nullable', Rule::in(['weight', 'package'])],
            'item_cost' => ['nullable', 'numeric', 'min:0'],
            'inventory_actual' => ['nullable', 'integer', 'min:0'],
            'inventory_to_add' => ['nullable', 'integer', 'min:0'],
            'inventory_to_remove' => ['nullable', 'integer', 'min:0'],
            'processor_inventory' => ['nullable', 'integer', 'min:0'],
            'other_inventory' => ['nullable', 'integer', 'min:0'],
            'out_of_stock_threshold_inventory' => ['sometimes', 'nullable', 'integer', 'min:0'],
            'stock_out_threshold' => ['nullable', 'integer', 'min:0'],
            'storage_location' => ['nullable', 'string'], //  (custom_sort)
            'packing_group' =>  ['nullable', 'string', Rule::in($this->packingGroups)],
            'barcode' => ['nullable', 'string', 'max:255'],
            'vendor' => ['nullable', 'string'],
            'accounting_class_id' => ['nullable', 'string', 'max:255'],
            'visibility_status' => ['nullable', Rule::in(['visible', 'hidden', 'on', 'off', 'true', 'false', 'yes', 'no', 1, 0])],
            'tax_status' => ['nullable', Rule::in(['on', 'off', 'true', 'false', 'yes', 'no', 1, 0])],
            'meta_title' => ['nullable', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:255'],
            'tier_quantity' => ['nullable', 'integer', 'min:1'],
        ];
    }

    public function prepareForValidation(array $data)
    {
        if ($data['type'] ?? false) {
            $data['type'] = str($data['type'])->lower()->trim()->value();
        }

        if ($data['packing_group'] ?? false) {
            $data['packing_group'] = str($data['packing_group'])->lower()->trim()->value();
        }

        if ($data['visibility_status'] ?? false) {
            $data['visibility_status'] = str($data['visibility_status'])->lower()->trim()->value();
        }

        if ($data['tax_status'] ?? false) {
            $data['tax_status'] = str($data['tax_status'])->lower()->trim()->value();
        }

        if ($data['pricing_unit'] ?? false) {
            $data['pricing_unit'] = str($data['pricing_unit'])->lower()->trim()->value();
        }

        return $data;
    }

    public function updatePriceTiers($existingProduct, Collection $row): void
    {
        Price::updateOrCreate(
            [
                'product_id' => $existingProduct->id,
                'quantity' => $row['tier_quantity'] ?? 1
            ],
            [
                'unit_price' => formatCurrencyForDB($row['tier_unit_price']),
                'sale_unit_price' => formatCurrencyForDB($row['tier_sale_unit_price']) ?? null
            ]
        );
    }
}
